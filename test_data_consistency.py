#!/usr/bin/env python3
"""
测试数据一致性和修复效果的脚本
"""
import sys
import os
import torch
import numpy as np

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.data_loader import create_data_loaders


def test_event_split_consistency():
    """测试事件划分的一致性"""
    print("=" * 60)
    print("测试事件划分一致性")
    print("=" * 60)
    
    # 创建两次数据加载器，使用相同的随机种子
    data_dir = "ice_events"
    config = {
        'batch_size': 8,
        'sequence_length': 60,
        'prediction_horizon': 12,
        'num_workers': 0,  # 避免多进程问题
        'sample_ratio': 0.1,
        'random_seed': 42
    }
    
    train_loader1, val_loader1, test_loader1 = create_data_loaders(
        data_dir=data_dir, **config
    )
    
    train_loader2, val_loader2, test_loader2 = create_data_loaders(
        data_dir=data_dir, **config
    )
    
    # 检查事件划分是否一致
    train_events1 = set(train_loader1.dataset.event_ids)
    train_events2 = set(train_loader2.dataset.event_ids)
    
    val_events1 = set(val_loader1.dataset.event_ids)
    val_events2 = set(val_loader2.dataset.event_ids)
    
    test_events1 = set(test_loader1.dataset.event_ids)
    test_events2 = set(test_loader2.dataset.event_ids)
    
    print(f"训练事件一致性: {'✓' if train_events1 == train_events2 else '✗'}")
    print(f"验证事件一致性: {'✓' if val_events1 == val_events2 else '✗'}")
    print(f"测试事件一致性: {'✓' if test_events1 == test_events2 else '✗'}")
    
    # 检查事件不重叠
    all_events = train_events1 | val_events1 | test_events1
    total_events = len(train_events1) + len(val_events1) + len(test_events1)
    
    print(f"事件无重叠: {'✓' if len(all_events) == total_events else '✗'}")
    
    return train_loader1, val_loader1, test_loader1


def test_scaler_sharing():
    """测试标准化器共享"""
    print("\n" + "=" * 60)
    print("测试标准化器共享")
    print("=" * 60)
    
    data_dir = "ice_events"
    train_loader, val_loader, test_loader = create_data_loaders(
        data_dir=data_dir,
        batch_size=8,
        sequence_length=60,
        prediction_horizon=12,
        num_workers=0,
        sample_ratio=0.1,
        random_seed=42
    )
    
    train_dataset = train_loader.dataset
    val_dataset = val_loader.dataset
    test_dataset = test_loader.dataset
    
    # 检查标准化器是否已拟合
    train_fitted = hasattr(train_dataset.scaler_weather, 'mean_')
    val_fitted = hasattr(val_dataset.scaler_weather, 'mean_')
    test_fitted = hasattr(test_dataset.scaler_weather, 'mean_')
    
    print(f"训练集标准化器已拟合: {'✓' if train_fitted else '✗'}")
    print(f"验证集标准化器已拟合: {'✓' if val_fitted else '✗'}")
    print(f"测试集标准化器已拟合: {'✓' if test_fitted else '✗'}")
    
    # 检查是否共享同一个标准化器对象
    weather_shared = (val_dataset.scaler_weather is train_dataset.scaler_weather and 
                     test_dataset.scaler_weather is train_dataset.scaler_weather)
    road_shared = (val_dataset.scaler_road is train_dataset.scaler_road and 
                  test_dataset.scaler_road is train_dataset.scaler_road)
    
    print(f"天气标准化器共享: {'✓' if weather_shared else '✗'}")
    print(f"道路标准化器共享: {'✓' if road_shared else '✗'}")
    
    return train_loader, val_loader, test_loader


def test_batch_consistency():
    """测试批次数据一致性"""
    print("\n" + "=" * 60)
    print("测试批次数据一致性")
    print("=" * 60)
    
    data_dir = "ice_events"
    config = {
        'batch_size': 8,
        'sequence_length': 60,
        'prediction_horizon': 12,
        'num_workers': 0,
        'sample_ratio': 0.1,
        'random_seed': 42
    }
    
    # 设置torch随机种子
    torch.manual_seed(42)
    train_loader1, _, _ = create_data_loaders(data_dir=data_dir, **config)
    
    torch.manual_seed(42)
    train_loader2, _, _ = create_data_loaders(data_dir=data_dir, **config)
    
    # 获取第一个批次
    torch.manual_seed(42)
    batch1 = next(iter(train_loader1))
    
    torch.manual_seed(42)
    batch2 = next(iter(train_loader2))
    
    # 比较批次数据
    weather_same = torch.allclose(batch1['weather_history'], batch2['weather_history'])
    road_same = torch.allclose(batch1['road_history'], batch2['road_history'])
    target_same = torch.allclose(batch1['target'], batch2['target'])
    
    print(f"天气数据一致性: {'✓' if weather_same else '✗'}")
    print(f"道路数据一致性: {'✓' if road_same else '✗'}")
    print(f"目标数据一致性: {'✓' if target_same else '✗'}")
    
    return weather_same and road_same and target_same


def test_event_length_distribution():
    """测试事件长度分布"""
    print("\n" + "=" * 60)
    print("测试事件长度分布")
    print("=" * 60)
    
    data_dir = "ice_events"
    train_loader, val_loader, test_loader = create_data_loaders(
        data_dir=data_dir,
        batch_size=8,
        sequence_length=60,
        prediction_horizon=12,
        num_workers=0,
        sample_ratio=0.1,
        random_seed=42
    )
    
    # 获取每个数据集的事件长度信息
    def get_event_lengths(dataset):
        lengths = []
        for event_id in dataset.event_ids:
            event_data = dataset.all_events_data[event_id]
            lengths.append(len(event_data))
        return lengths
    
    train_lengths = get_event_lengths(train_loader.dataset)
    val_lengths = get_event_lengths(val_loader.dataset)
    test_lengths = get_event_lengths(test_loader.dataset)
    
    print(f"训练集事件数: {len(train_lengths)}, 平均长度: {np.mean(train_lengths):.1f}")
    print(f"验证集事件数: {len(val_lengths)}, 平均长度: {np.mean(val_lengths):.1f}")
    print(f"测试集事件数: {len(test_lengths)}, 平均长度: {np.mean(test_lengths):.1f}")
    
    # 计算序列数量
    def count_sequences(dataset):
        return len(dataset.sequences)
    
    train_seq_count = count_sequences(train_loader.dataset)
    val_seq_count = count_sequences(val_loader.dataset)
    test_seq_count = count_sequences(test_loader.dataset)
    
    print(f"训练序列数: {train_seq_count}")
    print(f"验证序列数: {val_seq_count}")
    print(f"测试序列数: {test_seq_count}")


def main():
    """主测试函数"""
    print("开始数据一致性测试...")
    
    try:
        # 测试事件划分一致性
        train_loader, val_loader, test_loader = test_event_split_consistency()
        
        # 测试标准化器共享
        test_scaler_sharing()
        
        # 测试批次数据一致性
        batch_consistent = test_batch_consistency()
        
        # 测试事件长度分布
        test_event_length_distribution()
        
        print("\n" + "=" * 60)
        print("测试总结")
        print("=" * 60)
        print(f"所有测试: {'✓ 通过' if batch_consistent else '✗ 失败'}")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
