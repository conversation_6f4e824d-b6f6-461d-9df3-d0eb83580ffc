# 道路结冰预测系统

基于LSTM Encoder-Decoder架构的道路结冰预测系统，能够利用历史和未来天气数据以及道路观测数据来预测未来时刻的道路结冰概率。

## 系统架构

### 模型设计
- **Encoder**: 处理天气数据（包含历史和未来信息）
  - 历史天气数据使用双向LSTM处理
  - 未来天气数据使用单向LSTM处理
  - 融合历史和未来信息生成上下文向量

- **Decoder**: 处理道路观测数据并预测结冰概率
  - 结合道路历史数据和天气上下文
  - 使用注意力机制增强特征表示
  - 逐步预测未来时刻的结冰概率

### 数据特征
- **天气特征** (7维): 气温、湿度、气压、风速、风向、降雨量、降雨强度
- **道路特征** (4维): 路面温度、水膜厚度、冰点温度、盐度
- **目标变量**: is_icing (结冰概率)

### 数据处理改进
- **按事件划分**: 训练/验证/测试集按事件划分，避免数据泄漏
- **长度均衡**: 根据事件长度均匀分配，确保各集合数据分布平衡
- **随机抽样**: 支持从每个事件中随机抽取部分序列，提高训练效率
- **标准化器共享**: 验证集和测试集共享训练集拟合的标准化器
- **可重现性**: 通过固定随机种子确保数据划分和批次顺序的一致性

## 项目结构

```
ice_prediction/
├── src/
│   ├── __init__.py
│   ├── data_loader.py      # 数据加载和预处理
│   ├── model.py           # LSTM Encoder-Decoder模型
│   ├── train.py           # 训练脚本
│   ├── evaluate.py        # 评估脚本
│   └── utils.py           # 工具函数
├── ice_events/            # 数据目录
│   ├── ice_event_001.csv
│   ├── ice_event_002.csv
│   └── ...
├── config.json           # 配置文件
├── main.py              # 主程序入口
└── README_MODEL.md      # 本文件
```

## 安装依赖

确保您的conda环境中已安装以下依赖：

```bash
# 基础依赖
conda install pytorch torchvision torchaudio -c pytorch
conda install pandas numpy scikit-learn matplotlib seaborn
conda install tqdm tensorboard

# 或使用pip安装
pip install torch torchvision torchaudio
pip install pandas numpy scikit-learn matplotlib seaborn tqdm tensorboard
```

## 使用方法

### 1. 创建配置文件

```bash
python main.py create-config
```

这将创建默认的`config.json`配置文件，您可以根据需要修改参数。

### 2. 测试数据加载

```bash
python main.py test-data
```

验证数据加载是否正常工作。

### 2.1 测试数据一致性

```bash
python test_data_consistency.py
```

验证数据划分、标准化器共享和批次一致性是否正确。

### 3. 测试模型创建

```bash
python main.py test-model
```

验证模型是否能正确创建和运行。

### 4. 训练模型

```bash
python main.py train
```

开始训练模型。训练过程中会：
- 自动保存最佳模型到`checkpoints/best_model.pth`
- 定期保存检查点
- 使用TensorBoard记录训练日志
- 实施早停机制防止过拟合

### 5. 恢复训练

```bash
python main.py train --resume checkpoints/checkpoint_epoch_50.pth
```

从指定检查点恢复训练。

### 6. 评估模型

```bash
python main.py evaluate --model checkpoints/best_model.pth
```

评估训练好的模型，会生成：
- 详细的评估指标
- 混淆矩阵图
- 预测分布图
- 预测结果CSV文件

### 7. 自定义评估阈值

```bash
python main.py evaluate --model checkpoints/best_model.pth --threshold 0.3
```

使用自定义的分类阈值进行评估。

### 8. 可视化预测结果

#### 8.1 简化版可视化测试

```bash
# 仅测试数据加载
python simple_test_visualization.py

# 测试数据加载和模型预测
python simple_test_visualization.py --model checkpoints/best_model.pth
```

快速测试模型效果，生成简单的时间线对比图。

#### 8.2 完整版可视化分析

```bash
# 列出所有测试事件
python visualize_predictions.py --model checkpoints/best_model.pth --list-events

# 可视化指定事件
python visualize_predictions.py --model checkpoints/best_model.pth --event ice_event_020

# 自定义参数
python visualize_predictions.py \
    --model checkpoints/best_model.pth \
    --event ice_event_020 \
    --threshold 0.3 \
    --max-sequences 10 \
    --save-dir results
```

生成详细的可视化分析，包括：
- 实际结冰状态时间线
- 预测概率热图
- 预测vs实际对比图
- 详细的统计信息

## 配置参数说明

### 数据配置
- `sequence_length`: 输入序列长度（历史时间步数）
- `prediction_horizon`: 预测时间范围（未来时间步数）
- `batch_size`: 批次大小
- `sample_ratio`: 从每个事件中抽样的比例（0-1），用于控制数据量
- `random_seed`: 随机种子，确保数据划分和批次顺序的可重现性

### 模型配置
- `hidden_dim`: LSTM隐藏层维度
- `num_layers`: LSTM层数
- `dropout`: Dropout概率

### 训练配置
- `learning_rate`: 学习率
- `num_epochs`: 训练轮数
- `loss_function`: 损失函数（'focal'或'bce'）
- `early_stopping_patience`: 早停耐心值

## 监控训练过程

使用TensorBoard监控训练：

```bash
tensorboard --logdir logs
```

然后在浏览器中访问 `http://localhost:6006`

## 模型特点

1. **处理时序数据**: 使用LSTM处理时间序列数据
2. **融合多源信息**: 同时利用天气和道路数据
3. **包含未来信息**: Encoder可以处理未来天气预报数据
4. **注意力机制**: 增强重要特征的表示
5. **类别不平衡处理**: 使用Focal Loss处理结冰事件稀少的问题
6. **多时间步预测**: 一次预测多个未来时刻

## 输出文件

### 训练输出
- `checkpoints/`: 模型检查点
- `logs/`: TensorBoard日志

### 评估输出
- `predictions/predictions.csv`: 详细预测结果
- `predictions/summary.json`: 预测汇总统计
- `predictions/plots/`: 可视化图表

## 性能优化建议

1. **数据预处理**: 确保数据质量，处理缺失值
2. **特征工程**: 可以添加时间特征（小时、季节等）
3. **模型调优**: 调整隐藏层维度、层数等超参数
4. **正则化**: 调整dropout和权重衰减
5. **损失函数**: 根据数据分布调整Focal Loss参数

## 故障排除

### 常见问题

1. **CUDA内存不足**: 减小batch_size或hidden_dim
2. **训练过慢**: 增加num_workers或使用更小的模型
3. **过拟合**: 增加dropout或减少模型复杂度
4. **欠拟合**: 增加模型容量或训练更多轮次

### 调试模式

可以在配置文件中设置较小的参数进行快速测试：

```json
{
  "training": {
    "batch_size": 8,
    "num_epochs": 5
  },
  "model": {
    "hidden_dim": 64
  }
}
```

## 扩展功能

系统设计具有良好的扩展性，可以：

1. 添加更多特征（如历史结冰记录）
2. 使用不同的模型架构（Transformer、GRU等）
3. 实现在线预测服务
4. 添加模型解释性分析
5. 集成多个模型进行集成学习

## 联系方式

如有问题或建议，请通过以下方式联系：
- 创建Issue
- 提交Pull Request
- 发送邮件
