#!/usr/bin/env python3
"""
交互式可视化脚本 - 允许用户选择时间段
"""
import os
import sys
import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import argparse
import json

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.data_loader import IceDataset
from src.model import create_model
from src.utils import load_checkpoint, load_config


def interactive_visualize(config_path, model_path, event_id=None):
    """
    交互式可视化预测结果
    
    Args:
        config_path: 配置文件路径
        model_path: 模型路径
        event_id: 事件ID
    """
    print("开始交互式可视化...")
    
    # 加载配置
    config = load_config(config_path)
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建数据集
    print("创建数据集...")
    train_dataset = IceDataset(
        data_dir=config['data']['data_dir'],
        sequence_length=config['data']['sequence_length'],
        prediction_horizon=config['data']['prediction_horizon'],
        mode='train',
        sample_ratio=config['data'].get('sample_ratio', 1.0),
        random_seed=config['data'].get('random_seed', 42)
    )
    
    test_dataset = IceDataset(
        data_dir=config['data']['data_dir'],
        sequence_length=config['data']['sequence_length'],
        prediction_horizon=config['data']['prediction_horizon'],
        mode='test',
        sample_ratio=1.0,
        random_seed=config['data'].get('random_seed', 42),
        shared_scalers=(train_dataset.scaler_weather, train_dataset.scaler_road)
    )
    
    print(f"测试集事件数: {len(test_dataset.event_ids)}")
    
    # 选择事件
    if event_id is None:
        if not test_dataset.event_ids:
            print("测试集中没有事件")
            return
        print("可用的测试事件:")
        for i, eid in enumerate(test_dataset.event_ids):
            event_data = test_dataset.all_events_data[eid]
            ice_ratio = event_data['is_icing'].mean()
            print(f"  {i+1}. {eid}: {len(event_data)} 时间点, 结冰比例: {ice_ratio:.3f}")
        
        while True:
            try:
                choice = input(f"请选择事件 (1-{len(test_dataset.event_ids)}) 或输入事件ID: ").strip()
                if choice.isdigit():
                    idx = int(choice) - 1
                    if 0 <= idx < len(test_dataset.event_ids):
                        event_id = test_dataset.event_ids[idx]
                        break
                elif choice in test_dataset.event_ids:
                    event_id = choice
                    break
                else:
                    print("无效选择，请重试")
            except KeyboardInterrupt:
                print("\n用户取消")
                return
    
    if event_id not in test_dataset.event_ids:
        print(f"事件 {event_id} 不在测试集中")
        return
    
    print(f"选择事件: {event_id}")
    
    # 获取事件数据
    event_data = test_dataset.all_events_data[event_id].copy()
    print(f"事件总长度: {len(event_data)} 时间点")
    
    # 显示时间范围
    timestamps = pd.to_datetime(event_data['unix_time'], unit='s')
    print(f"时间范围: {timestamps.iloc[0]} 到 {timestamps.iloc[-1]}")
    print(f"总时长: {(timestamps.iloc[-1] - timestamps.iloc[0]).total_seconds() / 3600:.1f} 小时")
    
    # 让用户选择时间段
    max_points = 2000  # 可视化的最大点数
    if len(event_data) > max_points:
        print(f"\n数据点过多 ({len(event_data)})，需要选择时间段进行可视化")
        print(f"建议查看 {max_points} 个连续时间点")
        
        while True:
            try:
                print("\n选择时间段:")
                print("1. 开始部分")
                print("2. 中间部分") 
                print("3. 结束部分")
                print("4. 自定义范围")
                
                choice = input("请选择 (1-4): ").strip()
                
                if choice == '1':
                    start_idx = 0
                    end_idx = min(max_points, len(event_data))
                    break
                elif choice == '2':
                    start_idx = (len(event_data) - max_points) // 2
                    end_idx = start_idx + max_points
                    break
                elif choice == '3':
                    end_idx = len(event_data)
                    start_idx = max(0, end_idx - max_points)
                    break
                elif choice == '4':
                    print(f"数据索引范围: 0 到 {len(event_data)-1}")
                    start_idx = int(input(f"起始索引 (0-{len(event_data)-max_points}): "))
                    end_idx = min(start_idx + max_points, len(event_data))
                    if start_idx < 0 or start_idx >= len(event_data):
                        print("无效的起始索引")
                        continue
                    break
                else:
                    print("无效选择，请重试")
            except (ValueError, KeyboardInterrupt):
                print("输入错误或用户取消")
                return
        
        # 截取数据
        event_data = event_data.iloc[start_idx:end_idx].reset_index(drop=True)
        timestamps = timestamps.iloc[start_idx:end_idx].reset_index(drop=True)
        print(f"截取范围: 索引 {start_idx} 到 {end_idx-1}")
        print(f"截取后时间范围: {timestamps.iloc[0]} 到 {timestamps.iloc[-1]}")
    
    # 加载模型
    print("加载模型...")
    model = create_model(config['model']).to(device)
    checkpoint = load_checkpoint(model_path)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    # 获取该事件的序列进行预测
    event_sequences = [seq for seq in test_dataset.sequences if seq['event_id'] == event_id]
    print(f"事件序列数: {len(event_sequences)}")
    
    if not event_sequences:
        print("该事件没有有效序列，只显示实际数据")
        predictions_list = []
    else:
        # 预测前几个序列
        max_sequences = min(3, len(event_sequences))
        predictions_list = []
        
        print(f"预测前 {max_sequences} 个序列...")
        with torch.no_grad():
            for i in range(max_sequences):
                sequence = event_sequences[i]
                
                weather_history = torch.FloatTensor(sequence['weather_history']).unsqueeze(0).to(device)
                weather_future = torch.FloatTensor(sequence['weather_future']).unsqueeze(0).to(device)
                road_history = torch.FloatTensor(sequence['road_history']).unsqueeze(0).to(device)
                
                pred = model(
                    weather_history, 
                    weather_future, 
                    road_history,
                    config['data']['prediction_horizon']
                )
                
                predictions_list.append({
                    'prediction': pred.cpu().numpy().flatten(),
                    'target': sequence['target']
                })
    
    # 创建可视化
    print("创建可视化...")
    fig, axes = plt.subplots(2, 1, figsize=(15, 10))
    
    # 子图1: 实际结冰状态
    axes[0].plot(timestamps, event_data['is_icing'], 'r-', linewidth=2, label='实际结冰状态')
    axes[0].fill_between(timestamps, 0, event_data['is_icing'], alpha=0.3, color='red')
    axes[0].set_ylabel('结冰状态')
    axes[0].set_title(f'事件 {event_id} - 实际结冰状态')
    axes[0].grid(True, alpha=0.3)
    axes[0].legend()
    axes[0].set_ylim(-0.1, 1.1)
    
    # 子图2: 预测对比
    if predictions_list:
        # 显示第一个序列的预测
        pred_data = predictions_list[0]
        predictions = pred_data['prediction']
        targets = pred_data['target']
        
        # 创建预测时间轴（假设从截取数据的中间开始预测）
        pred_start_idx = len(event_data) // 3
        pred_end_idx = min(pred_start_idx + len(predictions), len(event_data))
        
        if pred_end_idx > pred_start_idx:
            pred_timestamps = timestamps[pred_start_idx:pred_end_idx]
            actual_values = event_data['is_icing'].iloc[pred_start_idx:pred_end_idx].values
            pred_values = predictions[:len(pred_timestamps)]
            
            axes[1].plot(pred_timestamps, actual_values, 'r-', linewidth=2, label='实际结冰状态')
            axes[1].plot(pred_timestamps, pred_values, 'b-', linewidth=2, label='预测概率')
            axes[1].axhline(y=0.5, color='g', linestyle='--', alpha=0.7, label='分类阈值 (0.5)')
            
            axes[1].fill_between(pred_timestamps, 0, actual_values, alpha=0.3, color='red')
            axes[1].fill_between(pred_timestamps, 0, pred_values, alpha=0.3, color='blue')
            
            # 计算性能指标
            pred_binary = (pred_values > 0.5).astype(int)
            target_binary = actual_values.astype(int)
            accuracy = (pred_binary == target_binary).mean()
            
            axes[1].set_title(f'预测对比 - 准确率: {accuracy:.3f}')
            
            print(f"\n=== 预测性能 ===")
            print(f"准确率: {accuracy:.3f}")
            print(f"平均预测概率: {pred_values.mean():.3f}")
            print(f"实际结冰比例: {target_binary.mean():.3f}")
        else:
            axes[1].text(0.5, 0.5, '预测范围超出数据长度', 
                       ha='center', va='center', transform=axes[1].transAxes)
    else:
        axes[1].text(0.5, 0.5, '没有可用的预测序列', 
                   ha='center', va='center', transform=axes[1].transAxes)
    
    axes[1].set_ylabel('概率/状态')
    axes[1].set_xlabel('时间')
    axes[1].grid(True, alpha=0.3)
    axes[1].legend()
    axes[1].set_ylim(-0.1, 1.1)
    
    # 格式化时间轴
    for ax in axes:
        # 自动调整时间刻度
        total_hours = (timestamps.iloc[-1] - timestamps.iloc[0]).total_seconds() / 3600
        if total_hours > 24:
            ax.xaxis.set_major_locator(plt.MaxNLocator(8))
        else:
            ax.xaxis.set_major_locator(plt.MaxNLocator(12))
        
        # 旋转标签
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    plt.tight_layout()
    
    # 保存图表
    save_path = f'interactive_viz_{event_id}.png'
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"图表已保存: {save_path}")
    
    plt.show()
    
    print("可视化完成!")


def main():
    parser = argparse.ArgumentParser(description='交互式可视化道路结冰预测结果')
    parser.add_argument('--config', type=str, default='config.json', help='配置文件路径')
    parser.add_argument('--model', type=str, required=True, help='模型检查点路径')
    parser.add_argument('--event', type=str, help='指定事件ID')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.config):
        print(f"配置文件不存在: {args.config}")
        return
    
    if not os.path.exists(args.model):
        print(f"模型文件不存在: {args.model}")
        return
    
    interactive_visualize(args.config, args.model, args.event)


if __name__ == "__main__":
    main()
