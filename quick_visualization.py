#!/usr/bin/env python3
"""
快速可视化脚本 - 避免matplotlib刻度问题
"""
import os
import sys
import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import argparse
import json

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.data_loader import IceDataset
from src.model import create_model
from src.utils import load_checkpoint, load_config


def quick_visualize(config_path, model_path, event_id=None, max_data_points=1000):
    """
    快速可视化预测结果
    
    Args:
        config_path: 配置文件路径
        model_path: 模型路径
        event_id: 事件ID
        max_data_points: 最大数据点数
    """
    print("开始快速可视化...")
    
    # 加载配置
    config = load_config(config_path)
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建数据集
    print("创建数据集...")
    train_dataset = IceDataset(
        data_dir=config['data']['data_dir'],
        sequence_length=config['data']['sequence_length'],
        prediction_horizon=config['data']['prediction_horizon'],
        mode='train',
        sample_ratio=config['data'].get('sample_ratio', 1.0),
        random_seed=config['data'].get('random_seed', 42)
    )
    
    test_dataset = IceDataset(
        data_dir=config['data']['data_dir'],
        sequence_length=config['data']['sequence_length'],
        prediction_horizon=config['data']['prediction_horizon'],
        mode='test',
        sample_ratio=1.0,
        random_seed=config['data'].get('random_seed', 42),
        shared_scalers=(train_dataset.scaler_weather, train_dataset.scaler_road)
    )
    
    print(f"测试集事件数: {len(test_dataset.event_ids)}")
    
    # 选择事件
    if event_id is None:
        if not test_dataset.event_ids:
            print("测试集中没有事件")
            return
        event_id = test_dataset.event_ids[0]
    
    if event_id not in test_dataset.event_ids:
        print(f"事件 {event_id} 不在测试集中")
        print("可用事件:", test_dataset.event_ids)
        return
    
    print(f"选择事件: {event_id}")
    
    # 获取事件数据
    event_data = test_dataset.all_events_data[event_id].copy()
    print(f"原始数据长度: {len(event_data)}")
    
    # 数据采样以避免可视化问题
    if len(event_data) > max_data_points:
        print(f"采样到 {max_data_points} 个数据点")
        sample_indices = np.linspace(0, len(event_data)-1, max_data_points, dtype=int)
        event_data = event_data.iloc[sample_indices].reset_index(drop=True)
    
    # 加载模型
    print("加载模型...")
    model = create_model(config['model']).to(device)
    checkpoint = load_checkpoint(model_path)
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    # 获取该事件的序列进行预测
    event_sequences = [seq for seq in test_dataset.sequences if seq['event_id'] == event_id]
    print(f"事件序列数: {len(event_sequences)}")
    
    if not event_sequences:
        print("该事件没有有效序列")
        return
    
    # 预测前几个序列
    max_sequences = min(3, len(event_sequences))
    predictions_list = []
    targets_list = []
    
    print(f"预测前 {max_sequences} 个序列...")
    with torch.no_grad():
        for i in range(max_sequences):
            sequence = event_sequences[i]
            
            weather_history = torch.FloatTensor(sequence['weather_history']).unsqueeze(0).to(device)
            weather_future = torch.FloatTensor(sequence['weather_future']).unsqueeze(0).to(device)
            road_history = torch.FloatTensor(sequence['road_history']).unsqueeze(0).to(device)
            
            pred = model(
                weather_history, 
                weather_future, 
                road_history,
                config['data']['prediction_horizon']
            )
            
            predictions_list.append(pred.cpu().numpy().flatten())
            targets_list.append(sequence['target'])
    
    # 创建可视化
    print("创建可视化...")
    fig, axes = plt.subplots(2, 1, figsize=(15, 10))
    
    # 创建时间轴（使用索引而不是实际时间戳避免刻度问题）
    time_indices = np.arange(len(event_data))
    
    # 子图1: 完整事件的真实结冰状态
    axes[0].plot(time_indices, event_data['is_icing'], 'r-', linewidth=2, label='实际结冰状态')
    axes[0].fill_between(time_indices, 0, event_data['is_icing'], alpha=0.3, color='red')
    axes[0].set_ylabel('结冰状态')
    axes[0].set_title(f'事件 {event_id} - 实际结冰状态时间线')
    axes[0].grid(True, alpha=0.3)
    axes[0].legend()
    axes[0].set_ylim(-0.1, 1.1)
    
    # 设置x轴刻度（显示时间信息）
    if len(event_data) > 0:
        timestamps = pd.to_datetime(event_data['unix_time'], unit='s')
        # 选择几个代表性的时间点
        n_ticks = min(8, len(time_indices))
        tick_positions = np.linspace(0, len(time_indices)-1, n_ticks, dtype=int)
        tick_labels = [timestamps.iloc[i].strftime('%m-%d %H:%M') for i in tick_positions]
        
        axes[0].set_xticks(tick_positions)
        axes[0].set_xticklabels(tick_labels, rotation=45)
    
    # 子图2: 预测对比
    if predictions_list:
        seq_len = config['data']['sequence_length']
        pred_len = config['data']['prediction_horizon']
        
        # 简化：只显示第一个序列的预测
        predictions = predictions_list[0]
        targets = targets_list[0]
        
        # 假设从事件中间开始预测
        pred_start_idx = len(event_data) // 3
        pred_end_idx = min(pred_start_idx + len(predictions), len(event_data))
        
        if pred_end_idx > pred_start_idx:
            pred_indices = time_indices[pred_start_idx:pred_end_idx]
            actual_values = event_data['is_icing'].iloc[pred_start_idx:pred_end_idx].values
            pred_values = predictions[:len(pred_indices)]
            
            axes[1].plot(pred_indices, actual_values, 'r-', linewidth=2, label='实际结冰状态')
            axes[1].plot(pred_indices, pred_values, 'b-', linewidth=2, label='预测概率')
            axes[1].axhline(y=0.5, color='g', linestyle='--', alpha=0.7, label='分类阈值 (0.5)')
            
            axes[1].fill_between(pred_indices, 0, actual_values, alpha=0.3, color='red')
            axes[1].fill_between(pred_indices, 0, pred_values, alpha=0.3, color='blue')
            
            axes[1].set_ylabel('概率/状态')
            axes[1].set_xlabel('时间索引')
            axes[1].set_title('预测对比（第一个序列）')
            axes[1].grid(True, alpha=0.3)
            axes[1].legend()
            axes[1].set_ylim(-0.1, 1.1)
            
            # 计算性能指标
            pred_binary = (pred_values > 0.5).astype(int)
            target_binary = actual_values.astype(int)
            accuracy = (pred_binary == target_binary).mean()
            
            print(f"\n=== 预测性能 ===")
            print(f"准确率: {accuracy:.3f}")
            print(f"平均预测概率: {pred_values.mean():.3f}")
            print(f"实际结冰比例: {target_binary.mean():.3f}")
        else:
            axes[1].text(0.5, 0.5, '预测范围超出事件长度', 
                       ha='center', va='center', transform=axes[1].transAxes)
    else:
        axes[1].text(0.5, 0.5, '没有可用的预测序列', 
                   ha='center', va='center', transform=axes[1].transAxes)
    
    plt.tight_layout()
    
    # 保存图表
    save_path = f'quick_viz_{event_id}.png'
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"图表已保存: {save_path}")
    
    plt.show()
    
    print("可视化完成!")


def main():
    parser = argparse.ArgumentParser(description='快速可视化道路结冰预测结果')
    parser.add_argument('--config', type=str, default='config.json', help='配置文件路径')
    parser.add_argument('--model', type=str, required=True, help='模型检查点路径')
    parser.add_argument('--event', type=str, help='指定事件ID')
    parser.add_argument('--max-points', type=int, default=1000, help='最大数据点数')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.config):
        print(f"配置文件不存在: {args.config}")
        return
    
    if not os.path.exists(args.model):
        print(f"模型文件不存在: {args.model}")
        return
    
    quick_visualize(args.config, args.model, args.event, args.max_points)


if __name__ == "__main__":
    main()
