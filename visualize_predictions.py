#!/usr/bin/env python3
"""
可视化预测结果脚本
加载训练好的模型，选择测试集事件，在时间轴上绘制实际结冰时间和预测结冰时间
"""
import os
import sys
import torch
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import argparse
import json

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.data_loader import IceDataset
from src.model import create_model
from src.utils import load_checkpoint, load_config

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False


class PredictionVisualizer:
    """预测结果可视化器"""
    
    def __init__(self, config_path, model_path):
        """
        初始化可视化器
        
        Args:
            config_path: 配置文件路径
            model_path: 模型检查点路径
        """
        self.config = load_config(config_path)
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 加载模型
        self.model = create_model(self.config['model']).to(self.device)
        checkpoint = load_checkpoint(model_path)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.model.eval()
        
        # 创建数据集（需要先创建训练集来获取标准化器）
        train_dataset = IceDataset(
            data_dir=self.config['data']['data_dir'],
            sequence_length=self.config['data']['sequence_length'],
            prediction_horizon=self.config['data']['prediction_horizon'],
            mode='train',
            sample_ratio=self.config['data'].get('sample_ratio', 1.0),
            random_seed=self.config['data'].get('random_seed', 42)
        )

        # 创建测试数据集，共享标准化器
        self.test_dataset = IceDataset(
            data_dir=self.config['data']['data_dir'],
            sequence_length=self.config['data']['sequence_length'],
            prediction_horizon=self.config['data']['prediction_horizon'],
            mode='test',
            sample_ratio=1.0,  # 使用完整测试集
            random_seed=self.config['data'].get('random_seed', 42),
            shared_scalers=(train_dataset.scaler_weather, train_dataset.scaler_road)
        )
        
        print(f"模型加载完成，设备: {self.device}")
        print(f"测试集事件数: {len(self.test_dataset.event_ids)}")
        print(f"测试集序列数: {len(self.test_dataset.sequences)}")
    
    def get_event_data(self, event_id):
        """
        获取指定事件的完整数据
        
        Args:
            event_id: 事件ID
            
        Returns:
            event_data: 事件数据DataFrame
        """
        if event_id not in self.test_dataset.all_events_data:
            raise ValueError(f"事件 {event_id} 不在测试集中")
        
        return self.test_dataset.all_events_data[event_id].copy()
    
    def predict_event_sequences(self, event_id, max_sequences=None):
        """
        预测指定事件的所有序列

        Args:
            event_id: 事件ID
            max_sequences: 最大序列数量限制

        Returns:
            predictions: 预测结果列表
            targets: 真实标签列表
            start_indices: 序列起始索引列表
        """
        # 获取该事件的所有序列
        event_sequences = [seq for seq in self.test_dataset.sequences if seq['event_id'] == event_id]

        if max_sequences:
            event_sequences = event_sequences[:max_sequences]

        predictions = []
        targets = []
        start_indices = []

        # 获取事件的原始数据来计算正确的起始索引
        event_data = self.get_event_data(event_id)
        seq_len = self.config['data']['sequence_length']
        pred_len = self.config['data']['prediction_horizon']

        # 重新创建序列以获取正确的起始索引
        # 这里我们需要模拟数据集创建时的逻辑
        max_possible_sequences = len(event_data) - seq_len - pred_len + 1
        if max_possible_sequences <= 0:
            return [], [], []

        # 使用与数据集相同的随机种子和抽样逻辑
        np.random.seed(self.config['data'].get('random_seed', 42) + hash(event_id) % 10000)
        sample_ratio = self.config['data'].get('sample_ratio', 1.0)
        num_sequences = max(1, int(max_possible_sequences * sample_ratio))
        actual_start_indices = np.random.choice(max_possible_sequences, size=num_sequences, replace=False)
        actual_start_indices.sort()

        with torch.no_grad():
            for i, sequence in enumerate(event_sequences):
                # 准备输入数据
                weather_history = torch.FloatTensor(sequence['weather_history']).unsqueeze(0).to(self.device)
                weather_future = torch.FloatTensor(sequence['weather_future']).unsqueeze(0).to(self.device)
                road_history = torch.FloatTensor(sequence['road_history']).unsqueeze(0).to(self.device)
                target = sequence['target']

                # 模型预测
                pred = self.model(
                    weather_history,
                    weather_future,
                    road_history,
                    self.config['data']['prediction_horizon']
                )

                predictions.append(pred.cpu().numpy().flatten())
                targets.append(target)

                # 使用正确的起始索引
                if i < len(actual_start_indices):
                    start_indices.append(actual_start_indices[i])
                else:
                    start_indices.append(i * 50)  # 备用方案

        return predictions, targets, start_indices
    
    def visualize_event_timeline(self, event_id, save_path=None, threshold=0.5, max_sequences=5, max_data_points=5000):
        """
        可视化事件时间线上的预测结果

        Args:
            event_id: 事件ID
            save_path: 保存路径
            threshold: 分类阈值
            max_sequences: 最大显示序列数
            max_data_points: 最大数据点数，超过则进行采样
        """
        # 获取事件数据
        event_data = self.get_event_data(event_id)

        # 如果数据点太多，进行采样
        if len(event_data) > max_data_points:
            print(f"数据点过多 ({len(event_data)})，采样到 {max_data_points} 个点")
            sample_indices = np.linspace(0, len(event_data)-1, max_data_points, dtype=int)
            event_data = event_data.iloc[sample_indices].reset_index(drop=True)
        
        # 获取预测结果
        predictions, targets, start_indices = self.predict_event_sequences(event_id, max_sequences)
        
        # 创建时间轴
        timestamps = pd.to_datetime(event_data['unix_time'], unit='s')
        
        # 创建图形
        fig, axes = plt.subplots(3, 1, figsize=(15, 12))
        
        # 子图1: 真实结冰状态
        axes[0].plot(timestamps, event_data['is_icing'], 'r-', linewidth=2, label='实际结冰状态')
        axes[0].fill_between(timestamps, 0, event_data['is_icing'], alpha=0.3, color='red')
        axes[0].set_ylabel('结冰状态')
        axes[0].set_title(f'事件 {event_id} - 实际结冰状态')
        axes[0].grid(True, alpha=0.3)
        axes[0].legend()
        axes[0].set_ylim(-0.1, 1.1)
        
        # 子图2: 预测概率热图
        if predictions:
            # 创建预测矩阵
            seq_len = self.config['data']['sequence_length']
            pred_len = self.config['data']['prediction_horizon']
            
            # 简化版本：只显示前几个序列的预测
            pred_matrix = np.zeros((len(predictions), len(timestamps)))
            
            for i, (pred, start_idx) in enumerate(zip(predictions, start_indices)):
                pred_start = start_idx + seq_len
                pred_end = min(pred_start + len(pred), len(timestamps))
                actual_pred_len = pred_end - pred_start
                
                if actual_pred_len > 0:
                    pred_matrix[i, pred_start:pred_end] = pred[:actual_pred_len]
            
            # 绘制热图 - 不使用extent，手动设置刻度
            im = axes[1].imshow(pred_matrix, aspect='auto', cmap='YlOrRd', vmin=0, vmax=1)

            # 手动设置x轴刻度和标签
            max_ticks = min(8, len(timestamps) // 500 + 1)  # 进一步减少刻度数量
            tick_positions = np.linspace(0, len(timestamps)-1, max_ticks)
            tick_indices = tick_positions.astype(int)

            axes[1].set_xticks(tick_positions)
            axes[1].set_xticklabels([timestamps.iloc[i].strftime('%m-%d %H:%M') for i in tick_indices], rotation=45)
            axes[1].set_xlim(0, len(timestamps)-1)
            
            axes[1].set_ylabel('预测序列')
            axes[1].set_title('预测概率热图')
            
            # 添加颜色条
            cbar = plt.colorbar(im, ax=axes[1])
            cbar.set_label('结冰概率')
        
        # 子图3: 平均预测概率 vs 实际状态
        if predictions:
            # 计算每个时间点的平均预测概率
            avg_predictions = np.zeros(len(timestamps))
            prediction_counts = np.zeros(len(timestamps))
            
            for i, (pred, start_idx) in enumerate(zip(predictions, start_indices)):
                pred_start = start_idx + seq_len
                pred_end = min(pred_start + len(pred), len(timestamps))
                actual_pred_len = pred_end - pred_start
                
                if actual_pred_len > 0:
                    avg_predictions[pred_start:pred_end] += pred[:actual_pred_len]
                    prediction_counts[pred_start:pred_end] += 1
            
            # 避免除零
            mask = prediction_counts > 0
            avg_predictions[mask] /= prediction_counts[mask]
            
            # 绘制对比图
            axes[2].plot(timestamps, event_data['is_icing'], 'r-', linewidth=2, label='实际结冰状态')
            axes[2].plot(timestamps[mask], avg_predictions[mask], 'b-', linewidth=2, label='平均预测概率')
            axes[2].axhline(y=threshold, color='g', linestyle='--', alpha=0.7, label=f'分类阈值 ({threshold})')
            
            # 填充区域
            axes[2].fill_between(timestamps, 0, event_data['is_icing'], alpha=0.3, color='red', label='实际结冰区域')
            axes[2].fill_between(timestamps[mask], 0, avg_predictions[mask], alpha=0.3, color='blue', label='预测结冰概率')
            
            axes[2].set_ylabel('概率/状态')
            axes[2].set_xlabel('时间')
            axes[2].set_title('预测概率 vs 实际状态对比')
            axes[2].grid(True, alpha=0.3)
            axes[2].legend()
            axes[2].set_ylim(-0.1, 1.1)
        
        # 格式化x轴 - 修复刻度过多的问题
        for ax in axes:
            # 计算合适的时间间隔
            total_hours = (timestamps.iloc[-1] - timestamps.iloc[0]).total_seconds() / 3600
            if total_hours > 48:
                interval = max(1, int(total_hours / 12))  # 最多12个刻度
                ax.xaxis.set_major_locator(mdates.HourLocator(interval=interval))
            elif total_hours > 24:
                ax.xaxis.set_major_locator(mdates.HourLocator(interval=4))
            elif total_hours > 12:
                ax.xaxis.set_major_locator(mdates.HourLocator(interval=2))
            else:
                ax.xaxis.set_major_locator(mdates.HourLocator(interval=1))

            ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"图表已保存到: {save_path}")
        
        plt.show()
        
        # 打印统计信息
        self.print_event_statistics(event_data, predictions, targets, threshold)
    
    def print_event_statistics(self, event_data, predictions, targets, threshold):
        """打印事件统计信息"""
        print(f"\n=== 事件统计信息 ===")
        print(f"事件总时长: {len(event_data)} 个时间点")
        print(f"实际结冰时间点: {event_data['is_icing'].sum()} 个")
        print(f"结冰比例: {event_data['is_icing'].mean():.3f}")
        
        if predictions and targets:
            all_predictions = np.concatenate(predictions)
            all_targets = np.concatenate(targets)
            
            pred_binary = (all_predictions > threshold).astype(int)
            
            accuracy = (pred_binary == all_targets).mean()
            precision = np.sum((pred_binary == 1) & (all_targets == 1)) / max(np.sum(pred_binary == 1), 1)
            recall = np.sum((pred_binary == 1) & (all_targets == 1)) / max(np.sum(all_targets == 1), 1)
            f1 = 2 * precision * recall / max(precision + recall, 1e-8)
            
            print(f"\n=== 预测性能 ===")
            print(f"预测序列数: {len(predictions)}")
            print(f"准确率: {accuracy:.3f}")
            print(f"精确率: {precision:.3f}")
            print(f"召回率: {recall:.3f}")
            print(f"F1分数: {f1:.3f}")
            print(f"平均预测概率: {all_predictions.mean():.3f}")
    
    def list_test_events(self):
        """列出所有测试事件"""
        print("=== 测试集事件列表 ===")
        for i, event_id in enumerate(self.test_dataset.event_ids):
            event_data = self.test_dataset.all_events_data[event_id]
            ice_ratio = event_data['is_icing'].mean()
            print(f"{i+1}. {event_id}: {len(event_data)} 时间点, 结冰比例: {ice_ratio:.3f}")


def main():
    parser = argparse.ArgumentParser(description='可视化道路结冰预测结果')
    parser.add_argument('--config', type=str, default='config.json', help='配置文件路径')
    parser.add_argument('--model', type=str, required=True, help='模型检查点路径')
    parser.add_argument('--event', type=str, help='指定事件ID')
    parser.add_argument('--threshold', type=float, default=0.5, help='分类阈值')
    parser.add_argument('--max-sequences', type=int, default=5, help='最大显示序列数')
    parser.add_argument('--save-dir', type=str, default='visualization_results', help='保存目录')
    parser.add_argument('--list-events', action='store_true', help='列出所有测试事件')
    
    args = parser.parse_args()
    
    # 检查文件存在性
    if not os.path.exists(args.config):
        print(f"配置文件不存在: {args.config}")
        return
    
    if not os.path.exists(args.model):
        print(f"模型文件不存在: {args.model}")
        return
    
    # 创建可视化器
    try:
        visualizer = PredictionVisualizer(args.config, args.model)
    except Exception as e:
        print(f"初始化可视化器失败: {e}")
        return
    
    # 列出事件
    if args.list_events:
        visualizer.list_test_events()
        return
    
    # 创建保存目录
    os.makedirs(args.save_dir, exist_ok=True)
    
    # 选择事件
    if args.event:
        if args.event not in visualizer.test_dataset.event_ids:
            print(f"事件 {args.event} 不在测试集中")
            print("可用事件:")
            visualizer.list_test_events()
            return
        event_id = args.event
    else:
        # 自动选择第一个事件
        if not visualizer.test_dataset.event_ids:
            print("测试集中没有事件")
            return
        event_id = visualizer.test_dataset.event_ids[0]
        print(f"自动选择事件: {event_id}")
    
    # 可视化
    save_path = os.path.join(args.save_dir, f'{event_id}_prediction_timeline.png')
    visualizer.visualize_event_timeline(
        event_id=event_id,
        save_path=save_path,
        threshold=args.threshold,
        max_sequences=args.max_sequences
    )


if __name__ == "__main__":
    main()
