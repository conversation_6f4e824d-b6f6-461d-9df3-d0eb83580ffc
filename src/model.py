"""
基于LSTM的Encoder-Decoder模型用于道路结冰预测
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, Optional


class WeatherEncoder(nn.Module):
    """天气数据编码器 - 处理历史和未来天气数据"""
    
    def __init__(self, 
                 input_dim: int,
                 hidden_dim: int,
                 num_layers: int = 2,
                 dropout: float = 0.2):
        """
        初始化天气编码器
        
        Args:
            input_dim: 输入特征维度（天气特征数量）
            hidden_dim: LSTM隐藏层维度
            num_layers: LSTM层数
            dropout: Dropout概率
        """
        super(WeatherEncoder, self).__init__()
        
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        
        # 历史天气数据LSTM
        self.history_lstm = nn.LSTM(
            input_size=input_dim,
            hidden_size=hidden_dim,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0,
            bidirectional=True
        )
        
        # 未来天气数据LSTM
        self.future_lstm = nn.LSTM(
            input_size=input_dim,
            hidden_size=hidden_dim,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0,
            bidirectional=False
        )
        
        # 特征融合层
        self.fusion = nn.Linear(hidden_dim * 2 + hidden_dim, hidden_dim)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, 
                weather_history: torch.Tensor,
                weather_future: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            weather_history: 历史天气数据 [batch_size, seq_len, weather_features]
            weather_future: 未来天气数据 [batch_size, pred_len, weather_features]
        
        Returns:
            context_vector: 上下文向量 [batch_size, hidden_dim]
            hidden_state: 隐藏状态 [num_layers, batch_size, hidden_dim]
        """
        batch_size = weather_history.size(0)
        
        # 处理历史天气数据（双向LSTM）
        history_output, (history_hidden, history_cell) = self.history_lstm(weather_history)
        
        # 处理未来天气数据（单向LSTM）
        future_output, (future_hidden, future_cell) = self.future_lstm(weather_future)
        
        # 获取最后时刻的输出
        history_last = history_output[:, -1, :]  # [batch_size, hidden_dim*2]
        future_last = future_output[:, -1, :]    # [batch_size, hidden_dim]
        
        # 融合历史和未来信息
        combined = torch.cat([history_last, future_last], dim=1)
        context_vector = self.fusion(combined)
        context_vector = self.dropout(F.relu(context_vector))
        
        # 使用未来LSTM的隐藏状态作为decoder的初始状态
        return context_vector, future_hidden


class RoadDecoder(nn.Module):
    """道路数据解码器 - 处理道路观测数据并预测结冰概率"""
    
    def __init__(self,
                 road_input_dim: int,
                 weather_context_dim: int,
                 hidden_dim: int,
                 output_dim: int = 1,
                 num_layers: int = 2,
                 dropout: float = 0.2):
        """
        初始化道路解码器
        
        Args:
            road_input_dim: 道路特征维度
            weather_context_dim: 天气上下文向量维度
            hidden_dim: LSTM隐藏层维度
            output_dim: 输出维度（预测时间步数）
            num_layers: LSTM层数
            dropout: Dropout概率
        """
        super(RoadDecoder, self).__init__()
        
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        self.output_dim = output_dim
        
        # 道路特征处理
        self.road_projection = nn.Linear(road_input_dim, hidden_dim // 2)
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(
            embed_dim=hidden_dim,
            num_heads=8,
            dropout=dropout,
            batch_first=True
        )
        
        # LSTM解码器
        self.lstm = nn.LSTM(
            input_size=hidden_dim // 2 + weather_context_dim,
            hidden_size=hidden_dim,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0
        )
        
        # 输出层
        self.output_projection = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, output_dim),
            nn.Sigmoid()  # 输出概率
        )
        
        self.dropout = nn.Dropout(dropout)
        
    def forward(self,
                road_history: torch.Tensor,
                weather_context: torch.Tensor,
                encoder_hidden: torch.Tensor,
                prediction_horizon: int) -> torch.Tensor:
        """
        前向传播
        
        Args:
            road_history: 历史道路数据 [batch_size, seq_len, road_features]
            weather_context: 天气上下文向量 [batch_size, context_dim]
            encoder_hidden: 编码器隐藏状态 [num_layers, batch_size, hidden_dim]
            prediction_horizon: 预测时间范围
        
        Returns:
            predictions: 预测的结冰概率 [batch_size, prediction_horizon]
        """
        batch_size = road_history.size(0)
        seq_len = road_history.size(1)
        
        # 处理道路特征
        road_features = self.road_projection(road_history)  # [batch_size, seq_len, hidden_dim//2]
        
        # 扩展天气上下文到序列长度
        weather_context_expanded = weather_context.unsqueeze(1).repeat(1, seq_len, 1)
        
        # 融合道路特征和天气上下文
        combined_input = torch.cat([road_features, weather_context_expanded], dim=2)
        
        # LSTM处理
        lstm_output, (hidden, cell) = self.lstm(combined_input, 
                                               (encoder_hidden, 
                                                torch.zeros_like(encoder_hidden)))
        
        # 注意力机制
        attended_output, _ = self.attention(lstm_output, lstm_output, lstm_output)
        attended_output = self.dropout(attended_output)
        
        # 预测未来时刻
        predictions = []
        current_hidden = hidden
        current_cell = cell
        
        # 使用最后一个时刻的输出作为初始输入
        last_output = attended_output[:, -1:, :]  # [batch_size, 1, hidden_dim]
        
        for t in range(prediction_horizon):
            # 预测当前时刻
            pred_output, (current_hidden, current_cell) = self.lstm(
                last_output, (current_hidden, current_cell)
            )
            
            # 生成概率预测
            prob = self.output_projection(pred_output.squeeze(1))  # [batch_size, 1]
            predictions.append(prob)
            
            # 更新输入（使用预测结果）
            last_output = pred_output
        
        # 合并预测结果
        predictions = torch.cat(predictions, dim=1)  # [batch_size, prediction_horizon]
        
        return predictions


class IcePredictionModel(nn.Module):
    """完整的道路结冰预测模型"""
    
    def __init__(self,
                 weather_input_dim: int = 7,
                 road_input_dim: int = 4,
                 hidden_dim: int = 128,
                 num_layers: int = 2,
                 dropout: float = 0.2):
        """
        初始化完整模型
        
        Args:
            weather_input_dim: 天气特征维度
            road_input_dim: 道路特征维度
            hidden_dim: 隐藏层维度
            num_layers: LSTM层数
            dropout: Dropout概率
        """
        super(IcePredictionModel, self).__init__()
        
        self.weather_encoder = WeatherEncoder(
            input_dim=weather_input_dim,
            hidden_dim=hidden_dim,
            num_layers=num_layers,
            dropout=dropout
        )
        
        self.road_decoder = RoadDecoder(
            road_input_dim=road_input_dim,
            weather_context_dim=hidden_dim,
            hidden_dim=hidden_dim,
            num_layers=num_layers,
            dropout=dropout
        )
        
    def forward(self,
                weather_history: torch.Tensor,
                weather_future: torch.Tensor,
                road_history: torch.Tensor,
                prediction_horizon: int = 12) -> torch.Tensor:
        """
        前向传播
        
        Args:
            weather_history: 历史天气数据
            weather_future: 未来天气数据
            road_history: 历史道路数据
            prediction_horizon: 预测时间范围
        
        Returns:
            predictions: 预测的结冰概率
        """
        # 编码天气信息
        weather_context, encoder_hidden = self.weather_encoder(
            weather_history, weather_future
        )
        
        # 解码并预测
        predictions = self.road_decoder(
            road_history, weather_context, encoder_hidden, prediction_horizon
        )
        
        return predictions


def create_model(config: dict) -> IcePredictionModel:
    """
    根据配置创建模型
    
    Args:
        config: 模型配置字典
    
    Returns:
        model: 初始化的模型
    """
    model = IcePredictionModel(
        weather_input_dim=config.get('weather_input_dim', 7),
        road_input_dim=config.get('road_input_dim', 4),
        hidden_dim=config.get('hidden_dim', 128),
        num_layers=config.get('num_layers', 2),
        dropout=config.get('dropout', 0.2)
    )
    
    return model


if __name__ == "__main__":
    # 测试模型
    config = {
        'weather_input_dim': 7,
        'road_input_dim': 4,
        'hidden_dim': 128,
        'num_layers': 2,
        'dropout': 0.2
    }
    
    model = create_model(config)
    
    # 创建测试数据
    batch_size = 8
    seq_len = 60
    pred_len = 12
    
    weather_history = torch.randn(batch_size, seq_len, 7)
    weather_future = torch.randn(batch_size, pred_len, 7)
    road_history = torch.randn(batch_size, seq_len, 4)
    
    # 前向传播测试
    with torch.no_grad():
        predictions = model(weather_history, weather_future, road_history, pred_len)
        print(f"模型输出形状: {predictions.shape}")
        print(f"预测值范围: [{predictions.min().item():.4f}, {predictions.max().item():.4f}]")
