"""
工具函数模块
"""
import torch
import numpy as np
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
import os
import json
from typing import Dict, Any


class EarlyStopping:
    """早停机制"""
    
    def __init__(self, patience=7, min_delta=0.001):
        self.patience = patience
        self.min_delta = min_delta
        self.counter = 0
        self.best_loss = float('inf')
        
    def __call__(self, val_loss):
        if val_loss < self.best_loss - self.min_delta:
            self.best_loss = val_loss
            self.counter = 0
        else:
            self.counter += 1
            
        return self.counter >= self.patience


def calculate_metrics(predictions: np.ndarray, targets: np.ndarray, threshold: float = 0.5) -> Dict[str, float]:
    """
    计算分类指标
    
    Args:
        predictions: 预测概率 [batch_size, prediction_horizon]
        targets: 真实标签 [batch_size, prediction_horizon]
        threshold: 分类阈值
    
    Returns:
        metrics: 指标字典
    """
    # 展平数组
    pred_flat = predictions.flatten()
    target_flat = targets.flatten()
    
    # 二值化预测
    pred_binary = (pred_flat > threshold).astype(int)
    target_binary = target_flat.astype(int)
    
    # 计算指标
    metrics = {
        'accuracy': accuracy_score(target_binary, pred_binary),
        'precision': precision_score(target_binary, pred_binary, zero_division=0),
        'recall': recall_score(target_binary, pred_binary, zero_division=0),
        'f1': f1_score(target_binary, pred_binary, zero_division=0),
        'auc': roc_auc_score(target_binary, pred_flat) if len(np.unique(target_binary)) > 1 else 0.0
    }
    
    return metrics


def save_checkpoint(model, optimizer, epoch, loss, filepath):
    """
    保存模型检查点
    
    Args:
        model: 模型
        optimizer: 优化器
        epoch: 当前轮次
        loss: 当前损失
        filepath: 保存路径
    """
    checkpoint = {
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'loss': loss
    }
    torch.save(checkpoint, filepath)


def load_checkpoint(filepath):
    """
    加载模型检查点
    
    Args:
        filepath: 检查点文件路径
    
    Returns:
        checkpoint: 检查点字典
    """
    checkpoint = torch.load(filepath, map_location='cpu')
    return checkpoint


def save_config(config: Dict[str, Any], filepath: str):
    """
    保存配置文件
    
    Args:
        config: 配置字典
        filepath: 保存路径
    """
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)


def load_config(filepath: str) -> Dict[str, Any]:
    """
    加载配置文件
    
    Args:
        filepath: 配置文件路径
    
    Returns:
        config: 配置字典
    """
    with open(filepath, 'r', encoding='utf-8') as f:
        config = json.load(f)
    return config


def count_parameters(model):
    """
    计算模型参数数量
    
    Args:
        model: PyTorch模型
    
    Returns:
        total_params: 总参数数量
        trainable_params: 可训练参数数量
    """
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    return total_params, trainable_params


def set_seed(seed: int = 42):
    """
    设置随机种子
    
    Args:
        seed: 随机种子
    """
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False


def get_device():
    """
    获取可用设备
    
    Returns:
        device: torch.device
    """
    if torch.cuda.is_available():
        device = torch.device('cuda')
        print(f'使用GPU: {torch.cuda.get_device_name()}')
    else:
        device = torch.device('cpu')
        print('使用CPU')
    
    return device


def format_time(seconds):
    """
    格式化时间
    
    Args:
        seconds: 秒数
    
    Returns:
        formatted_time: 格式化的时间字符串
    """
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    seconds = int(seconds % 60)
    
    if hours > 0:
        return f"{hours}h {minutes}m {seconds}s"
    elif minutes > 0:
        return f"{minutes}m {seconds}s"
    else:
        return f"{seconds}s"


class AverageMeter:
    """平均值计算器"""
    
    def __init__(self):
        self.reset()
    
    def reset(self):
        self.val = 0
        self.avg = 0
        self.sum = 0
        self.count = 0
    
    def update(self, val, n=1):
        self.val = val
        self.sum += val * n
        self.count += n
        self.avg = self.sum / self.count


def create_default_config():
    """
    创建默认配置
    
    Returns:
        config: 默认配置字典
    """
    config = {
        "data": {
            "data_dir": "ice_events",
            "sequence_length": 60,
            "prediction_horizon": 12,
            "num_workers": 4,
            "sample_ratio": 1.0,
            "random_seed": 42
        },
        "model": {
            "weather_input_dim": 7,
            "road_input_dim": 4,
            "hidden_dim": 128,
            "num_layers": 2,
            "dropout": 0.2
        },
        "training": {
            "batch_size": 32,
            "num_epochs": 100,
            "learning_rate": 0.001,
            "weight_decay": 1e-5,
            "optimizer": "adamw",
            "loss_function": "focal",
            "focal_alpha": 1.0,
            "focal_gamma": 2.0,
            "grad_clip": 1.0,
            "lr_factor": 0.5,
            "lr_patience": 5,
            "early_stopping_patience": 10,
            "early_stopping_min_delta": 0.001,
            "save_every": 10,
            "checkpoint_dir": "checkpoints",
            "log_dir": "logs"
        },
        "evaluation": {
            "threshold": 0.5,
            "save_predictions": True,
            "prediction_dir": "predictions"
        }
    }
    
    return config


if __name__ == "__main__":
    # 创建默认配置文件
    config = create_default_config()
    save_config(config, "config.json")
    print("已创建默认配置文件: config.json")
