"""
数据加载和预处理模块
"""
import os
import pandas as pd
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler
from typing import List, Tuple, Dict, Optional
import warnings
warnings.filterwarnings('ignore')


class IceDataset(Dataset):
    """道路结冰预测数据集"""

    def __init__(self,
                 data_dir: str,
                 sequence_length: int = 60,
                 prediction_horizon: int = 12,
                 train_ratio: float = 0.7,
                 val_ratio: float = 0.15,
                 mode: str = 'train',
                 normalize: bool = True,
                 sample_ratio: float = 1.0,
                 random_seed: int = 42):
        """
        初始化数据集

        Args:
            data_dir: 数据目录路径
            sequence_length: 输入序列长度（历史时间步数）
            prediction_horizon: 预测时间范围（未来时间步数）
            train_ratio: 训练集比例
            val_ratio: 验证集比例
            mode: 数据集模式 ('train', 'val', 'test')
            normalize: 是否标准化数据
            sample_ratio: 从每个事件中抽样的比例（0-1）
            random_seed: 随机种子，保证数据一致性
        """
        self.data_dir = data_dir
        self.sequence_length = sequence_length
        self.prediction_horizon = prediction_horizon
        self.mode = mode
        self.normalize = normalize
        self.sample_ratio = sample_ratio
        self.random_seed = random_seed

        # 设置随机种子
        np.random.seed(random_seed)
        
        # 定义特征列
        self.weather_features = [
            'weather_air_temp', 'weather_hum', 'weather_pressure',
            'weather_wind_speed', 'weather_wind_direction',
            'weather_rainfall', 'weather_rainfall_intensity'
        ]
        
        self.road_features = [
            'road_road_surface_temp', 'road_water_film_height',
            'road_freezing_temp', 'road_saltness'
        ]
        
        self.target_feature = 'is_icing'
        
        # 加载和预处理数据
        self.all_events_data = self._load_and_preprocess_data()

        # 按事件划分数据集
        self.event_ids = self._split_events_by_mode(train_ratio, val_ratio)

        # 创建标准化器（仅在训练模式下拟合）
        self.scaler_weather = StandardScaler() if normalize else None
        self.scaler_road = StandardScaler() if normalize else None

        # 创建序列数据
        self.sequences = self._create_sequences()
        
    def _load_and_preprocess_data(self) -> Dict[str, pd.DataFrame]:
        """加载并预处理所有CSV文件，按事件组织"""
        all_events_data = {}

        # 获取所有CSV文件
        csv_files = [f for f in os.listdir(self.data_dir) if f.endswith('.csv')]
        csv_files.sort()  # 确保文件顺序一致

        for file in csv_files:
            file_path = os.path.join(self.data_dir, file)
            df = pd.read_csv(file_path)

            # 添加文件标识符
            event_id = file.replace('.csv', '')
            df['event_id'] = event_id

            # 转换时间戳
            df['datetime'] = pd.to_datetime(df['unix_time'], unit='s')

            # 转换布尔值
            df[self.target_feature] = df[self.target_feature].astype(int)

            # 按时间排序
            df = df.sort_values('datetime').reset_index(drop=True)

            # 只保留足够长的事件
            if len(df) >= self.sequence_length + self.prediction_horizon:
                all_events_data[event_id] = df

        return all_events_data

    def _split_events_by_mode(self, train_ratio: float, val_ratio: float) -> List[str]:
        """按事件划分训练、验证、测试集"""
        all_event_ids = list(self.all_events_data.keys())
        all_event_ids.sort()  # 确保顺序一致

        # 设置随机种子并打乱事件顺序
        np.random.seed(self.random_seed)
        shuffled_events = all_event_ids.copy()
        np.random.shuffle(shuffled_events)

        # 计算分割点
        total_events = len(shuffled_events)
        train_end = int(total_events * train_ratio)
        val_end = int(total_events * (train_ratio + val_ratio))

        # 按模式返回对应的事件ID
        if self.mode == 'train':
            return shuffled_events[:train_end]
        elif self.mode == 'val':
            return shuffled_events[train_end:val_end]
        else:  # test
            return shuffled_events[val_end:]
    
    def _create_sequences(self) -> List[Dict]:
        """创建序列数据"""
        sequences = []

        # 收集所有训练数据用于拟合标准化器
        if self.normalize and self.mode == 'train':
            all_weather_data = []
            all_road_data = []

            for event_id in self.event_ids:
                event_data = self.all_events_data[event_id]
                all_weather_data.append(event_data[self.weather_features].values)
                all_road_data.append(event_data[self.road_features].values)

            # 拟合标准化器
            combined_weather = np.vstack(all_weather_data)
            combined_road = np.vstack(all_road_data)
            self.scaler_weather.fit(combined_weather)
            self.scaler_road.fit(combined_road)

        # 按事件处理
        for event_id in self.event_ids:
            event_data = self.all_events_data[event_id]

            # 提取特征
            weather_data = event_data[self.weather_features].values
            road_data = event_data[self.road_features].values
            target_data = event_data[self.target_feature].values

            # 标准化
            if self.normalize:
                weather_data = self.scaler_weather.transform(weather_data)
                road_data = self.scaler_road.transform(road_data)

            # 计算可能的序列数量
            max_sequences = len(event_data) - self.sequence_length - self.prediction_horizon + 1
            if max_sequences <= 0:
                continue

            # 根据抽样比例确定实际序列数量
            num_sequences = max(1, int(max_sequences * self.sample_ratio))

            # 随机选择起始位置
            np.random.seed(self.random_seed + hash(event_id) % 10000)  # 为每个事件设置不同但固定的种子
            start_indices = np.random.choice(max_sequences, size=num_sequences, replace=False)
            start_indices.sort()  # 保持时间顺序

            # 创建序列
            for start_idx in start_indices:
                # 历史天气数据（encoder输入）
                weather_seq = weather_data[start_idx:start_idx + self.sequence_length]

                # 未来天气数据（encoder输入，包含未来信息）
                future_weather_seq = weather_data[start_idx + self.sequence_length:start_idx + self.sequence_length + self.prediction_horizon]

                # 历史道路数据（decoder输入）
                road_seq = road_data[start_idx:start_idx + self.sequence_length]

                # 目标序列（未来is_icing）
                target_seq = target_data[start_idx + self.sequence_length:start_idx + self.sequence_length + self.prediction_horizon]

                sequences.append({
                    'weather_history': weather_seq,
                    'weather_future': future_weather_seq,
                    'road_history': road_seq,
                    'target': target_seq,
                    'event_id': event_id
                })

        return sequences
    
    def __len__(self) -> int:
        return len(self.sequences)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        sequence = self.sequences[idx]
        
        return {
            'weather_history': torch.FloatTensor(sequence['weather_history']),
            'weather_future': torch.FloatTensor(sequence['weather_future']),
            'road_history': torch.FloatTensor(sequence['road_history']),
            'target': torch.FloatTensor(sequence['target']),
            'event_id': sequence['event_id']
        }


def create_data_loaders(data_dir: str,
                       batch_size: int = 32,
                       sequence_length: int = 60,
                       prediction_horizon: int = 12,
                       num_workers: int = 4,
                       sample_ratio: float = 1.0,
                       random_seed: int = 42) -> Tuple[DataLoader, DataLoader, DataLoader]:
    """
    创建训练、验证和测试数据加载器

    Args:
        data_dir: 数据目录路径
        batch_size: 批次大小
        sequence_length: 输入序列长度
        prediction_horizon: 预测时间范围
        num_workers: 数据加载工作进程数
        sample_ratio: 从每个事件中抽样的比例（0-1）
        random_seed: 随机种子，保证数据一致性

    Returns:
        train_loader, val_loader, test_loader
    """
    
    # 创建数据集
    train_dataset = IceDataset(
        data_dir=data_dir,
        sequence_length=sequence_length,
        prediction_horizon=prediction_horizon,
        mode='train',
        sample_ratio=sample_ratio,
        random_seed=random_seed
    )

    val_dataset = IceDataset(
        data_dir=data_dir,
        sequence_length=sequence_length,
        prediction_horizon=prediction_horizon,
        mode='val',
        sample_ratio=sample_ratio,
        random_seed=random_seed
    )

    test_dataset = IceDataset(
        data_dir=data_dir,
        sequence_length=sequence_length,
        prediction_horizon=prediction_horizon,
        mode='test',
        sample_ratio=sample_ratio,
        random_seed=random_seed
    )
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True
    )
    
    return train_loader, val_loader, test_loader


if __name__ == "__main__":
    # 测试数据加载器
    data_dir = "../ice_events"
    train_loader, val_loader, test_loader = create_data_loaders(
        data_dir,
        sample_ratio=0.1,  # 只使用10%的数据进行测试
        random_seed=42
    )

    print(f"训练集批次数: {len(train_loader)}")
    print(f"验证集批次数: {len(val_loader)}")
    print(f"测试集批次数: {len(test_loader)}")

    # 查看一个批次的数据
    for batch in train_loader:
        print("批次数据形状:")
        for key, value in batch.items():
            if isinstance(value, torch.Tensor):
                print(f"  {key}: {value.shape}")
            else:
                print(f"  {key}: {type(value)}")
        break

    # 验证数据一致性
    print("\n验证数据一致性...")
    train_loader2, _, _ = create_data_loaders(
        data_dir,
        sample_ratio=0.1,
        random_seed=42  # 相同的随机种子
    )

    # 比较第一个批次
    batch1 = next(iter(train_loader))
    batch2 = next(iter(train_loader2))

    is_same = torch.allclose(batch1['weather_history'], batch2['weather_history'])
    print(f"数据一致性检查: {'通过' if is_same else '失败'}")
