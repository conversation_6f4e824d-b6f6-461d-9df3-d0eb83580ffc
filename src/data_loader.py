"""
数据加载和预处理模块
"""
import os
import pandas as pd
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler
from typing import List, Tuple, Dict, Optional
import warnings
warnings.filterwarnings('ignore')


class IceDataset(Dataset):
    """道路结冰预测数据集"""
    
    def __init__(self, 
                 data_dir: str,
                 sequence_length: int = 60,
                 prediction_horizon: int = 12,
                 train_ratio: float = 0.7,
                 val_ratio: float = 0.15,
                 mode: str = 'train',
                 normalize: bool = True):
        """
        初始化数据集
        
        Args:
            data_dir: 数据目录路径
            sequence_length: 输入序列长度（历史时间步数）
            prediction_horizon: 预测时间范围（未来时间步数）
            train_ratio: 训练集比例
            val_ratio: 验证集比例
            mode: 数据集模式 ('train', 'val', 'test')
            normalize: 是否标准化数据
        """
        self.data_dir = data_dir
        self.sequence_length = sequence_length
        self.prediction_horizon = prediction_horizon
        self.mode = mode
        self.normalize = normalize
        
        # 定义特征列
        self.weather_features = [
            'weather_air_temp', 'weather_hum', 'weather_pressure',
            'weather_wind_speed', 'weather_wind_direction',
            'weather_rainfall', 'weather_rainfall_intensity'
        ]
        
        self.road_features = [
            'road_road_surface_temp', 'road_water_film_height',
            'road_freezing_temp', 'road_saltness'
        ]
        
        self.target_feature = 'is_icing'
        
        # 加载和预处理数据
        self.data = self._load_and_preprocess_data()
        self.scaler_weather = StandardScaler() if normalize else None
        self.scaler_road = StandardScaler() if normalize else None
        
        # 分割数据集
        self.sequences = self._create_sequences()
        
    def _load_and_preprocess_data(self) -> pd.DataFrame:
        """加载并预处理所有CSV文件"""
        all_data = []
        
        # 获取所有CSV文件
        csv_files = [f for f in os.listdir(self.data_dir) if f.endswith('.csv')]
        csv_files.sort()  # 确保文件顺序一致
        
        for file in csv_files:
            file_path = os.path.join(self.data_dir, file)
            df = pd.read_csv(file_path)
            
            # 添加文件标识符
            df['event_id'] = file.replace('.csv', '')
            
            # 转换时间戳
            df['datetime'] = pd.to_datetime(df['unix_time'], unit='s')
            
            # 转换布尔值
            df[self.target_feature] = df[self.target_feature].astype(int)
            
            all_data.append(df)
        
        # 合并所有数据
        combined_data = pd.concat(all_data, ignore_index=True)
        
        # 按时间排序
        combined_data = combined_data.sort_values(['event_id', 'datetime']).reset_index(drop=True)
        
        return combined_data
    
    def _create_sequences(self) -> List[Dict]:
        """创建序列数据"""
        sequences = []
        
        # 按事件分组处理
        for event_id in self.data['event_id'].unique():
            event_data = self.data[self.data['event_id'] == event_id].copy()
            
            # 确保数据足够长
            if len(event_data) < self.sequence_length + self.prediction_horizon:
                continue
            
            # 提取特征
            weather_data = event_data[self.weather_features].values
            road_data = event_data[self.road_features].values
            target_data = event_data[self.target_feature].values
            
            # 标准化（仅在训练模式下拟合）
            if self.normalize:
                if self.mode == 'train':
                    weather_data = self.scaler_weather.fit_transform(weather_data)
                    road_data = self.scaler_road.fit_transform(road_data)
                else:
                    weather_data = self.scaler_weather.transform(weather_data)
                    road_data = self.scaler_road.transform(road_data)
            
            # 创建滑动窗口序列
            for i in range(len(event_data) - self.sequence_length - self.prediction_horizon + 1):
                # 历史天气数据（encoder输入）
                weather_seq = weather_data[i:i + self.sequence_length]
                
                # 未来天气数据（encoder输入，包含未来信息）
                future_weather_seq = weather_data[i + self.sequence_length:i + self.sequence_length + self.prediction_horizon]
                
                # 历史道路数据（decoder输入）
                road_seq = road_data[i:i + self.sequence_length]
                
                # 目标序列（未来is_icing）
                target_seq = target_data[i + self.sequence_length:i + self.sequence_length + self.prediction_horizon]
                
                sequences.append({
                    'weather_history': weather_seq,
                    'weather_future': future_weather_seq,
                    'road_history': road_seq,
                    'target': target_seq,
                    'event_id': event_id
                })
        
        # 分割数据集
        total_sequences = len(sequences)
        train_end = int(total_sequences * 0.7)
        val_end = int(total_sequences * 0.85)
        
        if self.mode == 'train':
            sequences = sequences[:train_end]
        elif self.mode == 'val':
            sequences = sequences[train_end:val_end]
        else:  # test
            sequences = sequences[val_end:]
        
        return sequences
    
    def __len__(self) -> int:
        return len(self.sequences)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        sequence = self.sequences[idx]
        
        return {
            'weather_history': torch.FloatTensor(sequence['weather_history']),
            'weather_future': torch.FloatTensor(sequence['weather_future']),
            'road_history': torch.FloatTensor(sequence['road_history']),
            'target': torch.FloatTensor(sequence['target']),
            'event_id': sequence['event_id']
        }


def create_data_loaders(data_dir: str,
                       batch_size: int = 32,
                       sequence_length: int = 60,
                       prediction_horizon: int = 12,
                       num_workers: int = 4) -> Tuple[DataLoader, DataLoader, DataLoader]:
    """
    创建训练、验证和测试数据加载器
    
    Args:
        data_dir: 数据目录路径
        batch_size: 批次大小
        sequence_length: 输入序列长度
        prediction_horizon: 预测时间范围
        num_workers: 数据加载工作进程数
    
    Returns:
        train_loader, val_loader, test_loader
    """
    
    # 创建数据集
    train_dataset = IceDataset(
        data_dir=data_dir,
        sequence_length=sequence_length,
        prediction_horizon=prediction_horizon,
        mode='train'
    )
    
    val_dataset = IceDataset(
        data_dir=data_dir,
        sequence_length=sequence_length,
        prediction_horizon=prediction_horizon,
        mode='val'
    )
    
    test_dataset = IceDataset(
        data_dir=data_dir,
        sequence_length=sequence_length,
        prediction_horizon=prediction_horizon,
        mode='test'
    )
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True
    )
    
    return train_loader, val_loader, test_loader


if __name__ == "__main__":
    # 测试数据加载器
    data_dir = "../ice_events"
    train_loader, val_loader, test_loader = create_data_loaders(data_dir)
    
    print(f"训练集批次数: {len(train_loader)}")
    print(f"验证集批次数: {len(val_loader)}")
    print(f"测试集批次数: {len(test_loader)}")
    
    # 查看一个批次的数据
    for batch in train_loader:
        print("批次数据形状:")
        for key, value in batch.items():
            if isinstance(value, torch.Tensor):
                print(f"  {key}: {value.shape}")
            else:
                print(f"  {key}: {type(value)}")
        break
