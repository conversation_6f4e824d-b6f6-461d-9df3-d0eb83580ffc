"""
模型训练脚本
"""
import os
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.tensorboard import SummaryWriter
import numpy as np
from tqdm import tqdm
import json
from datetime import datetime
import argparse

from data_loader import create_data_loaders
from model import create_model
from utils import EarlyStopping, calculate_metrics, save_checkpoint, load_checkpoint


class FocalLoss(nn.Module):
    """Focal Loss用于处理类别不平衡问题"""
    
    def __init__(self, alpha=1, gamma=2, reduction='mean'):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
        
    def forward(self, inputs, targets):
        bce_loss = nn.functional.binary_cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-bce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * bce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss


class Trainer:
    """训练器类"""
    
    def __init__(self, config):
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 创建模型
        self.model = create_model(config['model']).to(self.device)
        
        # 创建数据加载器
        self.train_loader, self.val_loader, self.test_loader = create_data_loaders(
            data_dir=config['data']['data_dir'],
            batch_size=config['training']['batch_size'],
            sequence_length=config['data']['sequence_length'],
            prediction_horizon=config['data']['prediction_horizon'],
            num_workers=config['data']['num_workers']
        )
        
        # 损失函数
        if config['training']['loss_function'] == 'focal':
            self.criterion = FocalLoss(
                alpha=config['training']['focal_alpha'],
                gamma=config['training']['focal_gamma']
            )
        else:
            self.criterion = nn.BCELoss()
        
        # 优化器
        if config['training']['optimizer'] == 'adam':
            self.optimizer = optim.Adam(
                self.model.parameters(),
                lr=config['training']['learning_rate'],
                weight_decay=config['training']['weight_decay']
            )
        elif config['training']['optimizer'] == 'adamw':
            self.optimizer = optim.AdamW(
                self.model.parameters(),
                lr=config['training']['learning_rate'],
                weight_decay=config['training']['weight_decay']
            )
        
        # 学习率调度器
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer,
            mode='min',
            factor=config['training']['lr_factor'],
            patience=config['training']['lr_patience'],
            verbose=True
        )
        
        # 早停
        self.early_stopping = EarlyStopping(
            patience=config['training']['early_stopping_patience'],
            min_delta=config['training']['early_stopping_min_delta']
        )
        
        # TensorBoard
        self.writer = SummaryWriter(config['training']['log_dir'])
        
        # 训练状态
        self.current_epoch = 0
        self.best_val_loss = float('inf')
        
    def train_epoch(self):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        num_batches = 0
        
        progress_bar = tqdm(self.train_loader, desc=f'Epoch {self.current_epoch + 1}')
        
        for batch in progress_bar:
            # 移动数据到设备
            weather_history = batch['weather_history'].to(self.device)
            weather_future = batch['weather_future'].to(self.device)
            road_history = batch['road_history'].to(self.device)
            targets = batch['target'].to(self.device)
            
            # 前向传播
            self.optimizer.zero_grad()
            predictions = self.model(
                weather_history, 
                weather_future, 
                road_history,
                self.config['data']['prediction_horizon']
            )
            
            # 计算损失
            loss = self.criterion(predictions, targets)
            
            # 反向传播
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(
                self.model.parameters(), 
                self.config['training']['grad_clip']
            )
            
            self.optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
            
            # 更新进度条
            progress_bar.set_postfix({'loss': loss.item()})
        
        avg_loss = total_loss / num_batches
        return avg_loss
    
    def validate(self):
        """验证模型"""
        self.model.eval()
        total_loss = 0
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for batch in tqdm(self.val_loader, desc='Validation'):
                # 移动数据到设备
                weather_history = batch['weather_history'].to(self.device)
                weather_future = batch['weather_future'].to(self.device)
                road_history = batch['road_history'].to(self.device)
                targets = batch['target'].to(self.device)
                
                # 前向传播
                predictions = self.model(
                    weather_history, 
                    weather_future, 
                    road_history,
                    self.config['data']['prediction_horizon']
                )
                
                # 计算损失
                loss = self.criterion(predictions, targets)
                total_loss += loss.item()
                
                # 收集预测和目标
                all_predictions.append(predictions.cpu().numpy())
                all_targets.append(targets.cpu().numpy())
        
        avg_loss = total_loss / len(self.val_loader)
        
        # 计算指标
        all_predictions = np.concatenate(all_predictions, axis=0)
        all_targets = np.concatenate(all_targets, axis=0)
        metrics = calculate_metrics(all_predictions, all_targets)
        
        return avg_loss, metrics
    
    def train(self):
        """完整训练流程"""
        print(f"开始训练，设备: {self.device}")
        print(f"模型参数数量: {sum(p.numel() for p in self.model.parameters()):,}")
        
        for epoch in range(self.config['training']['num_epochs']):
            self.current_epoch = epoch
            
            # 训练
            train_loss = self.train_epoch()
            
            # 验证
            val_loss, val_metrics = self.validate()
            
            # 学习率调度
            self.scheduler.step(val_loss)
            
            # 记录日志
            self.writer.add_scalar('Loss/Train', train_loss, epoch)
            self.writer.add_scalar('Loss/Validation', val_loss, epoch)
            self.writer.add_scalar('Learning_Rate', self.optimizer.param_groups[0]['lr'], epoch)
            
            for metric_name, metric_value in val_metrics.items():
                self.writer.add_scalar(f'Metrics/{metric_name}', metric_value, epoch)
            
            # 打印结果
            print(f'Epoch {epoch + 1}/{self.config["training"]["num_epochs"]}:')
            print(f'  Train Loss: {train_loss:.4f}')
            print(f'  Val Loss: {val_loss:.4f}')
            print(f'  Val Metrics: {val_metrics}')
            
            # 保存最佳模型
            if val_loss < self.best_val_loss:
                self.best_val_loss = val_loss
                save_checkpoint(
                    self.model, 
                    self.optimizer, 
                    epoch, 
                    val_loss,
                    os.path.join(self.config['training']['checkpoint_dir'], 'best_model.pth')
                )
                print(f'  保存最佳模型 (Val Loss: {val_loss:.4f})')
            
            # 早停检查
            if self.early_stopping(val_loss):
                print(f'早停触发，在第 {epoch + 1} 轮停止训练')
                break
            
            # 定期保存检查点
            if (epoch + 1) % self.config['training']['save_every'] == 0:
                save_checkpoint(
                    self.model, 
                    self.optimizer, 
                    epoch, 
                    val_loss,
                    os.path.join(self.config['training']['checkpoint_dir'], f'checkpoint_epoch_{epoch + 1}.pth')
                )
        
        self.writer.close()
        print('训练完成!')


def main():
    parser = argparse.ArgumentParser(description='训练道路结冰预测模型')
    parser.add_argument('--config', type=str, default='config.json', help='配置文件路径')
    parser.add_argument('--resume', type=str, default=None, help='恢复训练的检查点路径')
    
    args = parser.parse_args()
    
    # 加载配置
    with open(args.config, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # 创建必要的目录
    os.makedirs(config['training']['checkpoint_dir'], exist_ok=True)
    os.makedirs(config['training']['log_dir'], exist_ok=True)
    
    # 创建训练器
    trainer = Trainer(config)
    
    # 恢复训练（如果指定）
    if args.resume:
        checkpoint = load_checkpoint(args.resume)
        trainer.model.load_state_dict(checkpoint['model_state_dict'])
        trainer.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        trainer.current_epoch = checkpoint['epoch']
        trainer.best_val_loss = checkpoint['loss']
        print(f'从第 {trainer.current_epoch + 1} 轮恢复训练')
    
    # 开始训练
    trainer.train()


if __name__ == "__main__":
    main()
